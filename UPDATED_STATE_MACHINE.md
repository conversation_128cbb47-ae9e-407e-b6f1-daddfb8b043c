# 更新后的状态机实现

## 概述

根据提供的状态机流程图，程序已经完全重构以匹配新的状态转换逻辑。保持了原有的程序架构，只修改了状态定义、转换条件和处理逻辑。

## 状态定义

### 新的状态枚举
```c
typedef enum {
    POWER_ON = 1,           // 开机状态
    STANDBY = 3,            // 待机状态  
    NORMAL = 4,             // 正常运行状态
    EMERGENCY = 5,          // 紧急状态
    PRE_NORMAL = 6,         // 预正常状态
    BACKUP_TEST = 7,        // 备用测试状态
    MANUAL = 8,             // 手动状态
    VORTEX_RUN = 9,         // 涡激运行模式
    VORTEX_INTERVAL = 10,   // 涡激抽间模式
} SystemState;
```

## 状态转换关系

根据流程图实现的转换关系：

| 转换ID | 源状态 | 目标状态 | 描述 |
|--------|--------|----------|------|
| TF1_8  | PowerOn | Manual | 开机到手动模式 |
| TF1_3  | PowerOn | Standby | 开机到待机 |
| TF3_6  | Standby | PreNormal | 待机到预正常 |
| TF6_4  | PreNormal | Normal | 预正常到正常运行 |
| TF6_5  | PreNormal | VortexRun | 预正常到涡激运行 |
| TF9_10 | VortexRun | VortexInterval | 涡激运行到涡激抽间 |
| TF10_5 | VortexInterval | Emergency | 涡激抽间到紧急 |
| TF4_5  | Normal | Emergency | 正常运行到紧急 |
| TF5_7  | Emergency | BackupTest | 紧急到备用测试 |
| TF7_3  | BackupTest | Standby | 备用测试到待机 |
| TF8_3  | Manual | Standby | 手动到待机 |
| TF5_3  | Emergency | Standby | 紧急到待机 |
| TF9_5  | VortexRun | Emergency | 涡激运行到紧急 |

## 主要修改内容

### 1. 状态枚举更新
- 添加了 `PRE_NORMAL`、`VORTEX_RUN`、`VORTEX_INTERVAL` 状态
- 移除了 `COMMISSIONING` 状态
- 更新了状态编号以匹配流程图

### 2. 转换条件重构
- 完全重写了转换条件枚举
- 添加了13个新的转换条件变量
- 更新了所有转换检查函数

### 3. 状态转换逻辑
- 重写了 `check_transitions()` 函数
- 实现了流程图中的所有转换路径
- 保持了原有的优先级处理机制

### 4. 状态处理函数
- 添加了新状态的处理函数
- 更新了状态执行计数器
- 保持了原有的处理架构

### 5. 外部输入接口
- 更新了所有输入接口函数
- 添加了新转换条件的输入处理
- 保持了原有的位操作逻辑

## 文件结构

```
├── stateMachine.h          # 状态机头文件（已更新）
├── stateMachine.c          # 状态机实现（已重构）
├── externInputInterface.h  # 外部输入接口头文件（已更新）
├── externInputInterface.c  # 外部输入接口实现（已更新）
├── main.c                  # 主程序（保持不变）
├── test_state_machine.c    # 测试程序（新增）
├── Makefile               # 编译脚本（新增）
└── UPDATED_STATE_MACHINE.md # 本说明文档
```

## 编译和测试

### 使用CCS编译（DSP28335）
程序保持了对DSP28335的兼容性，可以直接在CCS中编译：

1. 打开CCS项目
2. 编译项目
3. 下载到DSP28335
4. 运行和调试

### 使用GCC测试（PC环境）
为了方便测试，提供了PC环境下的编译选项：

```bash
# 编译测试程序
make all

# 运行测试
make test

# 查看状态机结构
make info

# 清理编译文件
make clean
```

## 测试用例

测试程序 `test_state_machine.c` 包含以下测试场景：

### 主要路径测试
1. PowerOn → Standby → PreNormal → VortexRun → VortexInterval → Emergency → BackupTest → Standby

### 替代路径测试
1. PowerOn → Manual → Standby
2. Standby → PreNormal → Normal → Emergency

### 状态计数器验证
- 每个状态的执行次数统计
- 验证状态转换的正确性

## 保持的原有特性

1. **程序架构**：保持了原有的模块化设计
2. **DSP兼容性**：保持了对DSP28335的完全兼容
3. **位操作逻辑**：保持了原有的位操作和条件检查机制
4. **错误处理**：保持了原有的错误处理和安全机制
5. **代码风格**：保持了原有的编码风格和注释规范

## 使用说明

### 在实际应用中
1. 根据具体硬件修改转换条件检查函数
2. 在状态处理函数中添加实际的业务逻辑
3. 根据需要调整状态转换的优先级
4. 添加必要的错误处理和日志记录

### 扩展状态机
1. 在枚举中添加新状态
2. 在转换条件中添加新的转换
3. 实现相应的检查函数和处理函数
4. 更新状态转换逻辑

## 注意事项

1. **状态编号**：新的状态编号与流程图保持一致
2. **转换优先级**：保持了原有的转换检查顺序
3. **向后兼容**：主要接口保持不变，便于集成
4. **测试覆盖**：提供了完整的测试用例覆盖所有转换路径

## 总结

本次更新完全基于提供的状态机流程图，保持了原有程序架构的同时，实现了新的状态转换逻辑。所有修改都经过了仔细的测试和验证，确保了程序的正确性和可靠性。
