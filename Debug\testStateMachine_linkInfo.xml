<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TMS320C2000 Linker PC v18.1.4.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x686a5fc7</link_time>
   <link_errors>0x0</link_errors>
   <output_file>testStateMachine.out</output_file>
   <entry_point>
      <name>_c_int00</name>
      <address>0x510e</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-2">
         <path>.\</path>
         <kind>object</kind>
         <file>CodeStartBranch.obj</file>
         <name>CodeStartBranch.obj</name>
      </input_file>
      <input_file id="fl-3">
         <path>.\</path>
         <kind>object</kind>
         <file>externInputInterface.obj</file>
         <name>externInputInterface.obj</name>
      </input_file>
      <input_file id="fl-4">
         <path>.\</path>
         <kind>object</kind>
         <file>main.obj</file>
         <name>main.obj</name>
      </input_file>
      <input_file id="fl-5">
         <path>.\</path>
         <kind>object</kind>
         <file>stateMachine.obj</file>
         <name>stateMachine.obj</name>
      </input_file>
      <input_file id="fl-9">
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>boot28.asm.obj</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memcpy.c.obj</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-19">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>startup.c.obj</name>
      </input_file>
      <input_file id="fl-1a">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-1b">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-1c">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>args_main.c.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-27">
         <name>.cinit</name>
         <load_address>0x4000</load_address>
         <run_address>0x4000</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-48">
         <name>.cinit</name>
         <load_address>0x4028</load_address>
         <run_address>0x4028</run_address>
         <size>0xe</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.cinit</name>
         <load_address>0x4036</load_address>
         <run_address>0x4036</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-60">
         <name>.cinit:__lock</name>
         <load_address>0x4040</load_address>
         <run_address>0x4040</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-64">
         <name>.cinit:__unlock</name>
         <load_address>0x4045</load_address>
         <run_address>0x4045</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-18">
         <name>.cinit</name>
         <load_address>0x404a</load_address>
         <run_address>0x404a</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-62">
         <name>.text</name>
         <load_address>0x5000</load_address>
         <run_address>0x5000</run_address>
         <size>0x10e</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-11">
         <name>.text</name>
         <load_address>0x510e</load_address>
         <run_address>0x510e</run_address>
         <size>0x56</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text</name>
         <load_address>0x5164</load_address>
         <run_address>0x5164</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-61">
         <name>.text</name>
         <load_address>0x518d</load_address>
         <run_address>0x518d</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-59">
         <name>.text</name>
         <load_address>0x51b5</load_address>
         <run_address>0x51b5</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text</name>
         <load_address>0x51d9</load_address>
         <run_address>0x51d9</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.text</name>
         <load_address>0x51f9</load_address>
         <run_address>0x51f9</run_address>
         <size>0x1d</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text</name>
         <load_address>0x5216</load_address>
         <run_address>0x5216</run_address>
         <size>0x19</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-21">
         <name>.text:retain</name>
         <load_address>0x522f</load_address>
         <run_address>0x522f</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text</name>
         <load_address>0x5239</load_address>
         <run_address>0x5239</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-58">
         <name>.text</name>
         <load_address>0x5242</load_address>
         <run_address>0x5242</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.text</name>
         <load_address>0x5244</load_address>
         <run_address>0x5244</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-12">
         <name>codestart</name>
         <load_address>0x406f</load_address>
         <run_address>0x406f</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5e">
         <name>ramfuncs</name>
         <load_address>0x4050</load_address>
         <run_address>0x4050</run_address>
         <size>0x1f</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2e">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-2d">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-81">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x0</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-19">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8018</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-20">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8010</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-28">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8000</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-49">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x800a</run_address>
         <size>0x6</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-66">
         <name>.ebss:__unlock</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8016</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.ebss:__lock</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8014</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-13">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd3</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17">
         <name>.debug_info</name>
         <load_address>0xd3</load_address>
         <run_address>0xd3</run_address>
         <size>0x50c</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0x5df</load_address>
         <run_address>0x5df</run_address>
         <size>0x5fd</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-26">
         <name>.debug_info</name>
         <load_address>0xbdc</load_address>
         <run_address>0xbdc</run_address>
         <size>0x11ff</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.debug_info</name>
         <load_address>0x1ddb</load_address>
         <run_address>0x1ddb</run_address>
         <size>0x176</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-33">
         <name>.debug_info</name>
         <load_address>0x1f51</load_address>
         <run_address>0x1f51</run_address>
         <size>0x58a</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_info</name>
         <load_address>0x24db</load_address>
         <run_address>0x24db</run_address>
         <size>0x485</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_info</name>
         <load_address>0x2960</load_address>
         <run_address>0x2960</run_address>
         <size>0x3f8</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_info</name>
         <load_address>0x2d58</load_address>
         <run_address>0x2d58</run_address>
         <size>0x3f5</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0x314d</load_address>
         <run_address>0x314d</run_address>
         <size>0x58f</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0x36dc</load_address>
         <run_address>0x36dc</run_address>
         <size>0x517</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_info</name>
         <load_address>0x3bf3</load_address>
         <run_address>0x3bf3</run_address>
         <size>0x4b2</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_info</name>
         <load_address>0x40a5</load_address>
         <run_address>0x40a5</run_address>
         <size>0x99</size>
      </object_component>
      <object_component id="oc-14">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x45</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_line</name>
         <load_address>0x45</load_address>
         <run_address>0x45</run_address>
         <size>0x69</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0xae</load_address>
         <run_address>0xae</run_address>
         <size>0xef</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2a">
         <name>.debug_line</name>
         <load_address>0x19d</load_address>
         <run_address>0x19d</run_address>
         <size>0x319</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x4b6</load_address>
         <run_address>0x4b6</run_address>
         <size>0x7e</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x534</load_address>
         <run_address>0x534</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_line</name>
         <load_address>0x584</load_address>
         <run_address>0x584</run_address>
         <size>0x62</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_line</name>
         <load_address>0x5e6</load_address>
         <run_address>0x5e6</run_address>
         <size>0x3e</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_line</name>
         <load_address>0x624</load_address>
         <run_address>0x624</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_line</name>
         <load_address>0x65e</load_address>
         <run_address>0x65e</run_address>
         <size>0x65</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x6c3</load_address>
         <run_address>0x6c3</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_line</name>
         <load_address>0x721</load_address>
         <run_address>0x721</run_address>
         <size>0x55</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-15">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.debug_abbrev</name>
         <load_address>0x21</load_address>
         <run_address>0x21</run_address>
         <size>0xaa</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-24">
         <name>.debug_abbrev</name>
         <load_address>0xcb</load_address>
         <run_address>0xcb</run_address>
         <size>0xf7</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2b">
         <name>.debug_abbrev</name>
         <load_address>0x1c2</load_address>
         <run_address>0x1c2</run_address>
         <size>0x18b</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_abbrev</name>
         <load_address>0x34d</load_address>
         <run_address>0x34d</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x393</load_address>
         <run_address>0x393</run_address>
         <size>0xe3</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x476</load_address>
         <run_address>0x476</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0x51e</load_address>
         <run_address>0x51e</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_abbrev</name>
         <load_address>0x586</load_address>
         <run_address>0x586</run_address>
         <size>0x66</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_abbrev</name>
         <load_address>0x5ec</load_address>
         <run_address>0x5ec</run_address>
         <size>0x130</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_abbrev</name>
         <load_address>0x71c</load_address>
         <run_address>0x71c</run_address>
         <size>0xb2</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_abbrev</name>
         <load_address>0x7ce</load_address>
         <run_address>0x7ce</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_abbrev</name>
         <load_address>0x8bc</load_address>
         <run_address>0x8bc</run_address>
         <size>0xf</size>
      </object_component>
      <object_component id="oc-16">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-25">
         <name>.debug_aranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2c">
         <name>.debug_aranges</name>
         <load_address>0x90</load_address>
         <run_address>0x90</run_address>
         <size>0xe0</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_aranges</name>
         <load_address>0x170</load_address>
         <run_address>0x170</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_aranges</name>
         <load_address>0x190</load_address>
         <run_address>0x190</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_aranges</name>
         <load_address>0x1b0</load_address>
         <run_address>0x1b0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_aranges</name>
         <load_address>0x1d0</load_address>
         <run_address>0x1d0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_aranges</name>
         <load_address>0x1f0</load_address>
         <run_address>0x1f0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_aranges</name>
         <load_address>0x210</load_address>
         <run_address>0x210</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_aranges</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_aranges</name>
         <load_address>0x268</load_address>
         <run_address>0x268</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0xd4</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-29">
         <name>.debug_frame</name>
         <load_address>0x134</load_address>
         <run_address>0x134</run_address>
         <size>0x294</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_frame</name>
         <load_address>0x3c8</load_address>
         <run_address>0x3c8</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_frame</name>
         <load_address>0x430</load_address>
         <run_address>0x430</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_frame</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_frame</name>
         <load_address>0x4c0</load_address>
         <run_address>0x4c0</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_frame</name>
         <load_address>0x508</load_address>
         <run_address>0x508</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_frame</name>
         <load_address>0x570</load_address>
         <run_address>0x570</run_address>
         <size>0x78</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x5e8</load_address>
         <run_address>0x5e8</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x4000</load_address>
         <run_address>0x4000</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-27"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.text</name>
         <load_address>0x5000</load_address>
         <run_address>0x5000</run_address>
         <size>0x245</size>
         <contents>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-11"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-5a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>codestart</name>
         <load_address>0x406f</load_address>
         <run_address>0x406f</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-12"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>ramfuncs</name>
         <load_address>0x4050</load_address>
         <run_address>0x4050</run_address>
         <size>0x1f</size>
         <contents>
            <object_component_ref idref="oc-5e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-2e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>vectors</name>
         <run_address>0x3fffc2</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x0</run_address>
         <size>0x300</size>
         <contents>
            <object_component_ref idref="oc-2d"/>
            <object_component_ref idref="oc-81"/>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.ebss</name>
         <run_address>0x8000</run_address>
         <size>0x19</size>
         <contents>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-28"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-5f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.esysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.econst</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.switch</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>IQmath</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>IQmathTables</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-75" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x413e</size>
         <contents>
            <object_component_ref idref="oc-13"/>
            <object_component_ref idref="oc-17"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-26"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-77" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x776</size>
         <contents>
            <object_component_ref idref="oc-14"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-2a"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-55"/>
         </contents>
      </logical_group>
      <logical_group id="lg-79" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8cb</size>
         <contents>
            <object_component_ref idref="oc-15"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-24"/>
            <object_component_ref idref="oc-2b"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7b" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x288</size>
         <contents>
            <object_component_ref idref="oc-16"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-25"/>
            <object_component_ref idref="oc-2c"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-57"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7d" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x630</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-29"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-54"/>
         </contents>
      </logical_group>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>ZONE0</name>
         <page_id>0x0</page_id>
         <origin>0x4000</origin>
         <length>0x1000</length>
         <used_space>0x71</used_space>
         <unused_space>0xf8f</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4000</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x4050</start_address>
               <size>0x1f</size>
               <logical_group_ref idref="lg-6"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x406f</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x4071</start_address>
               <size>0xf8f</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE1</name>
         <page_id>0x0</page_id>
         <origin>0x5000</origin>
         <length>0x1000</length>
         <used_space>0x245</used_space>
         <unused_space>0xdbb</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5000</start_address>
               <size>0x245</size>
               <logical_group_ref idref="lg-4"/>
            </allocated_space>
            <available_space>
               <start_address>0x5245</start_address>
               <size>0xdbb</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE2</name>
         <page_id>0x0</page_id>
         <origin>0x6000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE3</name>
         <page_id>0x0</page_id>
         <origin>0x7000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE4</name>
         <page_id>0x0</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE5</name>
         <page_id>0x0</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE6</name>
         <page_id>0x0</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE7</name>
         <page_id>0x0</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CSM_RSVD</name>
         <page_id>0x0</page_id>
         <origin>0x33ff80</origin>
         <length>0x76</length>
         <used_space>0x0</used_space>
         <unused_space>0x76</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM_PWL</name>
         <page_id>0x0</page_id>
         <origin>0x33fff8</origin>
         <length>0x8</length>
         <used_space>0x0</used_space>
         <unused_space>0x8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC_CAL</name>
         <page_id>0x0</page_id>
         <origin>0x380080</origin>
         <length>0x9</length>
         <used_space>0x0</used_space>
         <unused_space>0x9</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>RESET</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc0</origin>
         <length>0x2</length>
         <used_space>0x0</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>VECTORS</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc2</origin>
         <length>0x3e</length>
         <used_space>0x0</used_space>
         <unused_space>0x3e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>M0SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x0</origin>
         <length>0x400</length>
         <used_space>0x300</used_space>
         <unused_space>0x100</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x300</size>
               <logical_group_ref idref="lg-9"/>
            </allocated_space>
            <available_space>
               <start_address>0x300</start_address>
               <size>0x100</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>M1SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x400</origin>
         <length>0x400</length>
         <used_space>0x0</used_space>
         <unused_space>0x400</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PIEDATA</name>
         <page_id>0x1</page_id>
         <origin>0xce0</origin>
         <length>0x20</length>
         <used_space>0x0</used_space>
         <unused_space>0x20</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L0SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0x19</used_space>
         <unused_space>0xfe7</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x8000</start_address>
               <size>0x19</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <available_space>
               <start_address>0x8019</start_address>
               <size>0xfe7</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L1SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L2SARAM</name>
         <page_id>0x1</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L3SARAM</name>
         <page_id>0x1</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-0">
         <name>cinit</name>
         <value>0x4000</value>
      </symbol>
      <symbol id="sm-1">
         <name>___cinit__</name>
         <value>0x4000</value>
      </symbol>
      <symbol id="sm-2">
         <name>pinit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3">
         <name>___pinit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5">
         <name>___binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6">
         <name>__STACK_SIZE</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-7">
         <name>__STACK_END</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-8">
         <name>___c_args__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-9">
         <name>.text</name>
         <value>0x5000</value>
      </symbol>
      <symbol id="sm-a">
         <name>___text__</name>
         <value>0x5000</value>
      </symbol>
      <symbol id="sm-b">
         <name>etext</name>
         <value>0x5245</value>
      </symbol>
      <symbol id="sm-c">
         <name>___etext__</name>
         <value>0x5245</value>
      </symbol>
      <symbol id="sm-d">
         <name>___TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-e">
         <name>___TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>___TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-41">
         <name>code_start</name>
         <value>0x406f</value>
         <object_component_ref idref="oc-12"/>
      </symbol>
      <symbol id="sm-51">
         <name>_input_tf1_2</name>
         <value>0x51ec</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-52">
         <name>_DI</name>
         <value>0x8018</value>
         <object_component_ref idref="oc-19"/>
      </symbol>
      <symbol id="sm-53">
         <name>_input_tf0_1</name>
         <value>0x51d9</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-70">
         <name>_main</name>
         <value>0x4065</value>
         <object_component_ref idref="oc-5e"/>
      </symbol>
      <symbol id="sm-71">
         <name>_InitSystem</name>
         <value>0x4050</value>
         <object_component_ref idref="oc-5e"/>
      </symbol>
      <symbol id="sm-72">
         <name>_DefaultIsr</name>
         <value>0x522f</value>
         <object_component_ref idref="oc-21"/>
      </symbol>
      <symbol id="sm-73">
         <name>__c_int00</name>
         <value>0x51af</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-74">
         <name>_DelayMs</name>
         <value>0x518d</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-75">
         <name>_InitPieVectTable</name>
         <value>0x51ab</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-b7">
         <name>_commissioning</name>
         <value>0x8004</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-b8">
         <name>_normal</name>
         <value>0x8007</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-b9">
         <name>_SET_BIT</name>
         <value>0x5000</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-ba">
         <name>_manual</name>
         <value>0x8008</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-bb">
         <name>_CLR_BIT</name>
         <value>0x5009</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-bc">
         <name>_TF1_2_condition</name>
         <value>0x8000</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-bd">
         <name>_powerOn</name>
         <value>0x8001</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-be">
         <name>_standby</name>
         <value>0x8002</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-bf">
         <name>_TF0_1_condition</name>
         <value>0x8003</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-c0">
         <name>_emergency</name>
         <value>0x8006</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-c1">
         <name>_get_current_state</name>
         <value>0x5013</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-c2">
         <name>_current_state</name>
         <value>0x8005</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-c3">
         <name>_backupTest</name>
         <value>0x8009</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-c4">
         <name>_state_machine_process</name>
         <value>0x5096</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-df">
         <name>_c_int00</name>
         <value>0x510e</value>
         <object_component_ref idref="oc-11"/>
      </symbol>
      <symbol id="sm-e0">
         <name>__stack</name>
         <value>0x0</value>
         <object_component_ref idref="oc-2d"/>
      </symbol>
      <symbol id="sm-ef">
         <name>_copy_in</name>
         <value>0x51b5</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-100">
         <name>_memcpy</name>
         <value>0x51f9</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-10c">
         <name>__system_pre_init</name>
         <value>0x5242</value>
         <object_component_ref idref="oc-58"/>
      </symbol>
      <symbol id="sm-119">
         <name>__system_post_cinit</name>
         <value>0x5244</value>
         <object_component_ref idref="oc-5a"/>
      </symbol>
      <symbol id="sm-12d">
         <name>C$$EXIT</name>
         <value>0x5164</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-12e">
         <name>_exit</name>
         <value>0x5166</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-12f">
         <name>___TI_cleanup_ptr</name>
         <value>0x800c</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-130">
         <name>___TI_enable_exit_profile_output</name>
         <value>0x800a</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-131">
         <name>_abort</name>
         <value>0x5164</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-132">
         <name>___TI_dtors_ptr</name>
         <value>0x800e</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-146">
         <name>__unlock</name>
         <value>0x8016</value>
         <object_component_ref idref="oc-66"/>
      </symbol>
      <symbol id="sm-147">
         <name>__lock</name>
         <value>0x8014</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-148">
         <name>__register_lock</name>
         <value>0x523d</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-149">
         <name>__nop</name>
         <value>0x5241</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-14a">
         <name>__register_unlock</name>
         <value>0x5239</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-159">
         <name>__args_main</name>
         <value>0x5216</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
