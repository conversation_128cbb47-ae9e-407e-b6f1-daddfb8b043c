/*
// TI File $Revision: /main/11 $
// Checkin $Date: April 15, 2009   09:57:28 $
//###########################################################################
//
// FILE:    28335_RAM_lnk.cmd
//
// TITLE:   Linker Command File For 28335 examples that run out of RAM
//
//          This ONLY includes all SARAM blocks on the 28335 device.
//          This does not include flash or OTP.
//
//          Keep in mind that L0 and L1 are protected by the code
//          security module.
//
//          What this means is in most cases you will want to move to
//          another memory map file which has more memory defined.
//
//###########################################################################
// $TI Release:   $
// $Release Date:   $
//###########################################################################
*/

/* ======================================================
// For Code Composer Studio V2.2 and later
// ---------------------------------------
// In addition to this memory linker command file,
// add the header linker command file directly to the project.
// The header linker command file is required to link the
// peripheral structures to the proper locations within
// the memory map.
//
// The header linker files are found in <base>\DSP2833x_Headers\cmd
//
// For BIOS applications add:      DSP2833x_Headers_BIOS.cmd
// For nonBIOS applications add:   DSP2833x_Headers_nonBIOS.cmd
========================================================= */

/* ======================================================
// For Code Composer Studio prior to V2.2
// --------------------------------------
// 1) Use one of the following -l statements to include the
// header linker command file in the project. The header linker
// file is required to link the peripheral structures to the proper
// locations within the memory map                                    */

/* Uncomment this line to include file only for non-BIOS applications */
/* -l DSP2833x_Headers_nonBIOS.cmd */

/* Uncomment this line to include file only for BIOS applications */
/* -l DSP2833x_Headers_BIOS.cmd */

/* 2) In your project add the path to <base>\DSP2833x_headers\cmd to the
   library search path under project->build options, linker tab,
   library search path (-i).
/*========================================================= */

/* Define the memory block start/length for the F28335
   PAGE 0 will be used to organize program sections
   PAGE 1 will be used to organize data sections

   Notes:
         Memory blocks on F28335 are uniform (ie same
         physical memory) in both PAGE 0 and PAGE 1.
         That is the same memory region should not be
         defined for both PAGE 0 and PAGE 1.
         Doing so will result in corruption of program
         and/or data.

         L0/L1/L2 and L3 memory blocks are mirrored - that is
         they can be accessed in high memory or low memory.
         For simplicity only one instance is used in this
         linker file.

         Contiguous SARAM memory blocks can be combined
         if required to create a larger memory block.
*/


MEMORY {
    PAGE 0:    /* Program Memory */
        /* Memory (RAM/FLASH/OTP) blocks can be moved to PAGE1 for data allocation */
        ZONE0       : origin = 0x004000, length = 0x001000     /* SARAM L0 on the 28335 */
        ZONE1       : origin = 0x005000, length = 0x001000     /* SARAM L1 on the 28335 */
        ZONE2       : origin = 0x006000, length = 0x001000     /* SARAM L2 on the 28335 */
        ZONE3       : origin = 0x007000, length = 0x001000     /* SARAM L3 on the 28335 */
        ZONE4       : origin = 0x008000, length = 0x001000     /* SARAM L4 on the 28335 */
        ZONE5       : origin = 0x009000, length = 0x001000     /* SARAM L5 on the 28335 */
        ZONE6       : origin = 0x00A000, length = 0x001000     /* SARAM L6 on the 28335 */
        ZONE7       : origin = 0x00B000, length = 0x001000     /* SARAM L7 on the 28335 */
        CSM_RSVD    : origin = 0x33FF80, length = 0x000076     /* Part of FLASHA.  Program with all 0x0000 when CSM is in use. */
        CSM_PWL     : origin = 0x33FFF8, length = 0x000008     /* Part of FLASHA.  CSM password locations in FLASHA */
        ADC_CAL     : origin = 0x380080, length = 0x000009
        RESET       : origin = 0x3FFFC0, length = 0x000002
        VECTORS     : origin = 0x3FFFC2, length = 0x00003E

    PAGE 1:    /* Data Memory */
        /* Memory (RAM/FLASH/OTP) blocks can be moved to PAGE0 for program allocation */
        /* Registers remain on PAGE1                                                  */
        M0SARAM     : origin = 0x000000, length = 0x000400
        M1SARAM     : origin = 0x000400, length = 0x000400
        PIEDATA     : origin = 0x000CE0, length = 0x000020
        L0SARAM     : origin = 0x008000, length = 0x001000
        L1SARAM     : origin = 0x009000, length = 0x001000
        L2SARAM     : origin = 0x00A000, length = 0x001000
        L3SARAM     : origin = 0x00B000, length = 0x001000
}

SECTIONS {
    /* Allocate program areas: */
    .cinit              : > ZONE0,      PAGE = 0
    .pinit              : > ZONE0,      PAGE = 0
    .text               : > ZONE1,      PAGE = 0
    codestart           : > ZONE0,      PAGE = 0
    ramfuncs            : > ZONE0,      PAGE = 0
    .reset              : > RESET,      PAGE = 0, TYPE = DSECT /* not used, */
    vectors             : > VECTORS     PAGE = 0, TYPE = DSECT /* not used, */

    /* Allocate uninitalized data sections: */
    .stack              : > M0SARAM,    PAGE = 1
    .ebss               : > L0SARAM,    PAGE = 1
    .esysmem            : > L1SARAM,    PAGE = 1

    /* Initalized sections go in Flash */
    /* For SDFlash to program these, they must be allocated to page 0 */
    .econst             : > L0SARAM,    PAGE = 1
    .switch             : > L0SARAM,    PAGE = 1

    /* Allocate IQ math areas: */
    IQmath              : > ZONE1,      PAGE = 0            /* Math Code */
    IQmathTables        : > ZONE2,      PAGE = 0, TYPE = NOLOAD

    /* .reset is a standard section used by the compiler.  It contains the */
    /* the address of the start of _c_int00 for C Code.   /*
    /* When using the boot ROM this section and the CPU vector */
    /* table is not needed.  Thus the default type is set here to  */
    /* DSECT  */
}

/*
//===========================================================================
// End of file.
//===========================================================================
*/
