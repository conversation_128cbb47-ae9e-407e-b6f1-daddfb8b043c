#include <stdio.h>

/* State enumeration */
typedef enum {
    POWER_ON = 1,           // Power on state
    STANDBY = 3,            // Standby state
    NORMAL = 4,             // Normal operation state
    EMERGENCY = 5,          // Emergency state
    PRE_NORMAL = 6,         // Pre-normal state
    BACKUP_TEST = 7,        // Backup power test state
    MANUAL = 8,             // Manual state
    VORTEX_RUN = 9,         // 涡激运行模式
    VORTEX_INTERVAL = 10,   // 涡激抽间模式
    STATE_MAX               // Total number of states (for array size)
} SystemState;

/* Transition condition enumeration */
typedef enum {
    TF1_8 = 0,   // PowerOn->Manual
    TF1_3 = 1,   // PowerOn->Standby
    TF3_6 = 2,   // Standby->PreNormal
    TF6_4 = 3,   // PreNormal->Normal
    TF6_5 = 4,   // PreNormal->VortexRun
    TF9_10 = 5,  // VortexRun->VortexInterval
    TF10_5 = 6,  // VortexInterval->Emergency
    TF4_5 = 7,   // Normal->Emergency
    TF5_7 = 8,   // Emergency->BackupTest
    TF7_3 = 9,   // BackupTest->Standby
    TF8_3 = 10,  // Manual->Standby
    TF5_3 = 11,  // Emergency->Standby
    TF9_5 = 12,  // VortexRun->Emergency
} TransitionCondition;

// Transition condition variables
extern int TF1_8_condition;
extern int TF1_3_condition;
extern int TF3_6_condition;
extern int TF6_4_condition;
extern int TF6_5_condition;
extern int TF9_10_condition;
extern int TF10_5_condition;
extern int TF4_5_condition;
extern int TF5_7_condition;
extern int TF7_3_condition;
extern int TF8_3_condition;
extern int TF5_3_condition;
extern int TF9_5_condition;

extern int SET_BIT(int num, int bit_pos);
extern int CLR_BIT(int num, int bit_pos);

// State Action flag
extern int powerOn;
extern int standby;
extern int preNormal;
extern int normal;
extern int emergency;
extern int backupTest;
extern int manual;
extern int vortexRun;
extern int vortexInterval;

extern SystemState current_state;

/* ��ȡ��ǰ״̬ */
SystemState get_current_state(void);

//���״̬��ת����������ǰ״̬���߼�����
/* ״̬����ѭ���������� */
void state_machine_process(void);
