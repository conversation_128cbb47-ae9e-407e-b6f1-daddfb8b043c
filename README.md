# DSP28335 状态机程序 - CCS调试问题修复

## 问题描述
在CCS软件仿真器调试时出现错误：
```
Break at address "0x0" with no debug information available, or outside of program code.
```

## 问题原因分析
1. **缺少DSP28335特定的初始化代码**：程序没有正确的系统初始化
2. **链接器文件配置错误**：内存映射不正确，导致程序跳转到无效地址
3. **缺少中断向量表初始化**：未初始化的中断可能导致跳转到0x0地址
4. **缺少看门狗禁用**：看门狗复位可能导致程序异常
5. **stdio.h依赖问题**：在DSP环境中使用printf可能导致链接错误

## 修复方案

### 1. 修复main.c
- 移除stdio.h依赖，避免链接问题
- 添加DSP28335特定的寄存器定义
- 添加看门狗禁用代码
- 添加PIE向量表初始化
- 添加默认中断服务程序防止跳转到0x0

### 2. 修复链接器文件 (28335_RAM_lnk.cmd)
- 使用正确的DSP28335内存映射
- 正确配置SARAM区域
- 添加必要的段分配

### 3. 添加启动代码 (CodeStartBranch.asm)
- 提供正确的程序入口点
- 确保从正确地址开始执行

## 修复后的文件列表

### 主要文件
1. **main.c** - 修复后的状态机主程序
2. **28335_RAM_lnk.cmd** - 修复后的链接器命令文件
3. **CodeStartBranch.asm** - 新增的启动代码文件

### 关键修复点

#### main.c修复内容：
- ✅ 移除stdio.h依赖
- ✅ 添加DSP28335寄存器定义
- ✅ 添加看门狗禁用代码
- ✅ 添加PIE向量表初始化
- ✅ 添加默认中断服务程序
- ✅ 添加简化的C运行时入口点
- ✅ 使用英文注释避免编码问题

#### 28335_RAM_lnk.cmd修复内容：
- ✅ 使用正确的DSP28335内存映射
- ✅ 正确配置SARAM区域 (L0-L7, M0-M1)
- ✅ 添加ramfuncs段分配
- ✅ 添加向量表和复位向量配置

#### CodeStartBranch.asm新增内容：
- ✅ 提供正确的程序入口点
- ✅ 分支到C运行时初始化

## 使用说明

### 编译和调试步骤
1. 在CCS中打开项目
2. 确保所有文件都已添加到项目中
3. 编译项目 (Ctrl+B)
4. 连接目标设备或启动仿真器
5. 加载程序到目标
6. 开始调试

### 预期行为
- 程序应该从POWER_ON状态开始
- 不会再出现"Break at address 0x0"错误
- 状态机按照设计的流程正常运行：
  - POWER_ON → STAND_BY → CAP_TEST → NORMAL_OPERATION
  - 支持手动模式、高级模式、紧急模式切换

### 调试验证步骤
1. **设置断点**：在main()函数开始处设置断点
2. **启动调试**：按F5或点击Debug按钮
3. **检查初始状态**：验证currentState = 0 (POWER_ON)
4. **单步执行**：观察状态机的执行流程
5. **监视变量**：添加currentState, stateTimer到Watch窗口

### 常见问题排查
- 如果仍然出现0x0地址错误，检查链接器文件是否正确加载
- 如果程序不响应，检查看门狗是否被正确禁用
- 如果状态不切换，检查DelayMs函数是否正常工作

## 简化状态机程序说明

## 概述
这是一个基于图片设计的简化状态机程序，专门为DSP28335优化，具有以下特点：
- 简单直观，易于理解和修改
- 专门针对CCS调试环境优化
- 具有防冲突机制，紧急状态具有最高优先级
- 不依赖复杂的数据结构，适合DSP环境

## 状态定义
程序包含8个主要状态：
1. **POWER_ON (0)** - 系统上电
2. **STAND_BY (1)** - 系统待机
3. **CAP_TEST (2)** - 电容装置测试
4. **NORMAL_OPERATION (3)** - 系统正常运行
5. **ADVANCED_RUN (4)** - 高级运行模式
6. **ADVANCED_CONTROL (5)** - 高级控制模式
7. **MANUAL_MODE (6)** - 手动模式
8. **EMERGENCY_MODE2 (7)** - 紧急模式2

## 防冲突机制
- **紧急状态优先级最高**：无论在任何状态，紧急触发都会立即跳转到紧急模式
- **状态检查顺序**：先检查紧急条件，再检查当前状态的正常跳转条件
- **条件互斥**：每次只处理一个条件，避免同时满足多个条件的冲突

## 主要函数

### 核心函数
```c
void checkConditions(void);      // 检查跳转条件并执行状态切换
void executeCurrentState(void);  // 执行当前状态的逻辑
```

### 全局变量
```c
uint32_t currentState;  // 当前状态
uint32_t stateTimer;    // 状态计时器
```

## 移植到CCS/Keil的步骤

1. **移除printf调试**：将printf语句注释掉或删除
2. **替换条件检测**：将模拟的条件变量替换为实际的硬件检测
3. **添加硬件头文件**：根据目标芯片添加相应头文件
4. **集成到主循环**：在while(1)循环中调用状态机函数

## CCS移植示例

```c
// 移除printf，添加硬件头文件
// #include <stdio.h>  // 注释掉
#include "DSP28x_Project.h"  // 添加CCS头文件

int main(void)
{
    // 硬件初始化
    InitSysCtrl();

    currentState = POWER_ON;
    stateTimer = 0;

    while(1)
    {
        checkConditions();
        executeCurrentState();

        // 添加适当的延时
        DELAY_US(1000);  // 1ms延时
    }
}
```

## Keil移植示例

```c
// 移除printf，添加硬件头文件
// #include <stdio.h>  // 注释掉
#include "stm32f10x.h"  // 添加Keil头文件

int main(void)
{
    // 硬件初始化
    SystemInit();

    currentState = POWER_ON;
    stateTimer = 0;

    while(1)
    {
        checkConditions();
        executeCurrentState();

        // 添加适当的延时
        Delay_ms(1);  // 1ms延时
    }
}
```

## 自定义条件检测

将模拟条件替换为实际硬件检测：

```c
// 原代码（模拟）：
if (stateTimer == 50) {
    capTestRequest = true;
}

// 替换为（实际硬件）：
if (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0) == SET) {
    capTestRequest = true;
}
```

## 编译测试

当前环境下编译：
```bash
gcc -o simple_state_machine main.c
./simple_state_machine.exe
```

## 程序特点

1. **简单结构**：使用简单的switch-case结构，易于理解
2. **防冲突**：紧急状态具有最高优先级，确保安全
3. **易移植**：最少的依赖，容易移植到不同平台
4. **易扩展**：添加新状态只需修改相应的case分支

## 状态跳转对应关系

程序实现了图中的所有状态跳转：
- 条件1: 上电完成 → 待机
- 条件2: 电容测试请求 → 电容测试
- 条件3: 正常启动 → 正常运行
- 条件4: 测试完成 → 正常运行
- 条件5: 手动请求 → 手动模式
- 条件6: 高级请求 → 高级运行
- 条件7: 紧急触发 → 紧急模式
- 其他条件按图中箭头实现
