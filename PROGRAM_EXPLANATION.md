# DSP28335 状态机程序详细解释

## 文件概述
`main_detailed.c` 是一个专门为DSP28335设计的状态机程序，解决了CCS调试时的"Break at address 0x0"问题。

## 程序结构详细解释

### 第1部分：编译器指令 (第23-26行)
```c
#pragma CODE_SECTION(main, "ramfuncs")
#pragma CODE_SECTION(InitSystem, "ramfuncs")
```
**作用**：告诉编译器将main函数和InitSystem函数放在RAM中执行
**原因**：RAM执行速度比Flash快，便于调试

### 第2部分：类型定义 (第33-40行)
```c
typedef unsigned long  uint32_t;
typedef unsigned short uint16_t;
typedef unsigned char  uint8_t;
typedef unsigned char  bool;
```
**作用**：定义基本数据类型
**原因**：避免包含标准头文件，防止链接问题

### 第3部分：DSP28335硬件定义 (第47-62行)
```c
#define EALLOW  asm(" EALLOW")
#define EDIS    asm(" EDIS")
#define WDCR    (*(volatile uint16_t *)0x7029)
```
**作用**：定义DSP28335特定的寄存器和汇编指令
**关键点**：
- `EALLOW/EDIS`：保护寄存器访问控制
- `WDCR`：看门狗控制寄存器
- `volatile`：防止编译器优化

### 第4部分：状态定义 (第69-76行)
```c
#define POWER_ON         0
#define STAND_BY         1
// ... 其他状态
```
**作用**：定义8个状态，对应状态图
**设计**：使用简单的#define而不是enum，便于调试观察

### 第5部分：全局变量 (第83-89行)
```c
static uint32_t currentState = POWER_ON;
static uint32_t stateTimer = 0;
static uint32_t systemTick = 0;
```
**作用**：状态机控制变量
**说明**：
- `currentState`：当前状态
- `stateTimer`：当前状态持续时间
- `systemTick`：全局时钟计数

### 第6部分：条件标志 (第96-106行)
```c
static bool powerOnComplete = false;
static bool capTestRequest = false;
// ... 其他条件
```
**作用**：表示状态转换条件
**实际应用**：这些标志应该由硬件传感器、用户输入或系统事件设置

## 核心函数详细解释

### InitSystem函数 (第200-245行)
这是解决"Break at 0x0"问题的关键函数：

#### 步骤1：禁用中断 (第207行)
```c
asm(" SETC INTM");
```
**作用**：在初始化期间禁用所有中断
**原因**：防止未初始化的中断处理程序被调用

#### 步骤2-3：看门狗禁用 (第213-217行)
```c
EALLOW;
WDKEY = WD_DISABLE_SEQ1;
WDKEY = WD_DISABLE_SEQ2;
WDCR = 0x0068;
```
**作用**：禁用看门狗定时器
**原因**：看门狗复位会导致调试中断

#### 步骤6：PIE向量表初始化 (第235行)
```c
InitPieVectTable();
```
**作用**：初始化中断向量表
**原因**：防止未处理的中断跳转到0x0地址

### checkConditions函数 (第262-382行)
实现防冲突机制的核心函数：

#### 优先级1：紧急条件 (第274-281行)
```c
if (emergencyTrigger) {
    emergencyTrigger = false;
    currentState = EMERGENCY_MODE2;
    stateTimer = 0;
    return;
}
```
**设计**：紧急条件具有最高优先级，可以从任何状态触发
**防冲突**：使用return立即退出，确保只处理一个条件

#### 优先级2：正常状态转换 (第284-382行)
```c
switch (currentState) {
    case POWER_ON:
        if (powerOnComplete) {
            // 状态转换逻辑
        }
        break;
    // ... 其他状态
}
```
**设计**：基于当前状态检查相应的转换条件
**防冲突**：每个状态只检查相关的转换条件

### executeCurrentState函数 (第398-547行)
执行当前状态逻辑的函数：

#### 计时器更新 (第408-409行)
```c
stateTimer++;
systemTick++;
```
**作用**：每次执行都更新计时器
**用途**：用于超时检测和状态持续时间统计

#### 状态特定逻辑 (第413-547行)
每个状态都有自己的处理逻辑：
- **POWER_ON**：模拟系统初始化
- **STAND_BY**：等待用户命令
- **CAP_TEST**：执行电容测试
- **NORMAL_OPERATION**：主要业务逻辑
- **其他状态**：各自的特定功能

### main函数 (第564-594行)
主程序循环：

#### 系统初始化 (第571行)
```c
InitSystem();
```
**关键**：必须首先调用，解决调试问题

#### 主循环 (第574-592行)
```c
while (1) {
    checkConditions();
    executeCurrentState();
    DelayMs(1);
    asm(" NOP");
}
```
**执行顺序**：
1. 检查状态转换条件
2. 执行当前状态逻辑
3. 延时防止CPU过载
4. 防止编译器优化

### _c_int00函数 (第612-628行)
C运行时入口点：

```c
void _c_int00(void) {
    asm(" SETC INTM");
    main();
    while(1) {
        asm(" ESTOP0");
    }
}
```
**作用**：提供正确的程序入口点
**解决问题**：防止链接器找不到入口点导致跳转到0x0

## 防冲突机制详解

### 1. 优先级机制
- **最高优先级**：紧急条件（可从任何状态触发）
- **正常优先级**：基于当前状态的转换条件

### 2. 单次处理原则
- 每次调用`checkConditions()`只处理一个条件
- 使用`return`和`else if`确保互斥

### 3. 标志清除机制
```c
if (powerOnComplete) {
    powerOnComplete = false;  // 立即清除标志
    // 执行状态转换
}
```
**作用**：防止同一条件被重复处理

## 调试问题解决方案

### 1. "Break at address 0x0"问题
**原因**：
- 缺少正确的程序入口点
- 未初始化的中断向量表
- 看门狗复位

**解决方案**：
- 提供`_c_int00`入口点
- 初始化PIE向量表
- 禁用看门狗
- 使用默认中断处理程序

### 2. 链接问题
**原因**：stdio.h在DSP环境中可能导致链接错误
**解决方案**：移除stdio.h依赖，使用自定义类型定义

### 3. 内存映射问题
**解决方案**：使用正确的链接器文件和内存配置

## 实际应用指导

### 1. 硬件适配
将模拟的条件标志替换为实际硬件检测：
```c
// 原代码（模拟）
if (stateTimer == 50) {
    capTestRequest = true;
}

// 实际应用
if (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0) == SET) {
    capTestRequest = true;
}
```

### 2. 扩展状态
添加新状态只需：
1. 定义新的状态常量
2. 在`checkConditions()`中添加转换逻辑
3. 在`executeCurrentState()`中添加状态处理

### 3. 性能优化
- 使用定时器中断代替DelayMs
- 实现事件驱动的状态机
- 添加状态变化回调函数

## 修复前后对比

| 问题类别 | 修复前 | 修复后 | 效果 |
|---------|--------|--------|------|
| **程序入口** | 缺少DSP入口点 | 提供`_c_int00`函数 | 解决0x0跳转 |
| **看门狗** | 未禁用 | 正确禁用序列 | 防止意外复位 |
| **中断处理** | 无默认处理 | `DefaultIsr`函数 | 防止0x0跳转 |
| **头文件** | 包含stdio.h | 移除依赖 | 避免链接错误 |
| **内存配置** | 错误的内存映射 | 正确的SARAM配置 | 程序正确加载 |
| **状态机** | 基本功能 | 防冲突机制 | 可靠的状态转换 |
| **调试支持** | 难以调试 | 详细注释+断点支持 | 易于理解和维护 |

## 使用建议

### 1. 开发阶段
- 使用`main_detailed.c`进行开发和调试
- 利用详细注释理解程序逻辑
- 在关键位置设置断点观察状态变化

### 2. 生产阶段
- 可以简化注释减少代码大小
- 移除模拟条件，添加实际硬件接口
- 根据需要优化性能

### 3. 移植到其他平台
- 修改硬件相关定义（第3部分）
- 调整初始化函数（InitSystem）
- 保持状态机逻辑不变

## 总结
这个程序通过正确的DSP28335初始化、防冲突的状态机设计和适当的错误处理，成功解决了CCS调试问题，同时提供了一个可靠、可扩展的状态机框架。每个函数都有详细的注释说明其作用和实现原理，便于理解和维护。
