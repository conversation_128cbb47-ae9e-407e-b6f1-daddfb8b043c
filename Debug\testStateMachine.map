******************************************************************************
             TMS320C2000 Linker PC v18.1.4                     
******************************************************************************
>> Linked Sun Jul  6 19:36:39 2025

OUTPUT FILE NAME:   <testStateMachine.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 0000510e


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  ZONE0                 00004000   00001000  00000071  00000f8f  RWIX
  ZONE1                 00005000   00001000  00000245  00000dbb  RWIX
  ZONE2                 00006000   00001000  00000000  00001000  RWIX
  ZONE3                 00007000   00001000  00000000  00001000  RWIX
  ZONE4                 00008000   00001000  00000000  00001000  RWIX
  ZONE5                 00009000   00001000  00000000  00001000  RWIX
  ZONE6                 0000a000   00001000  00000000  00001000  RWIX
  ZONE7                 0000b000   00001000  00000000  00001000  RWIX
  CSM_RSVD              0033ff80   00000076  00000000  00000076  RWIX
  CSM_PWL               0033fff8   00000008  00000000  00000008  RWIX
  ADC_CAL               00380080   00000009  00000000  00000009  RWIX
  RESET                 003fffc0   00000002  00000000  00000002  RWIX
  VECTORS               003fffc2   0000003e  00000000  0000003e  RWIX

PAGE 1:
  M0SARAM               00000000   00000400  00000300  00000100  RWIX
  M1SARAM               00000400   00000400  00000000  00000400  RWIX
  PIEDATA               00000ce0   00000020  00000000  00000020  RWIX
  L0SARAM               00008000   00001000  00000019  00000fe7  RWIX
  L1SARAM               00009000   00001000  00000000  00001000  RWIX
  L2SARAM               0000a000   00001000  00000000  00001000  RWIX
  L3SARAM               0000b000   00001000  00000000  00001000  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.cinit     0    00004000    00000050     
                  00004000    00000028     stateMachine.obj (.cinit)
                  00004028    0000000e     rts2800_fpu32.lib : exit.c.obj (.cinit)
                  00004036    0000000a     main.obj (.cinit)
                  00004040    00000005     rts2800_fpu32.lib : _lock.c.obj (.cinit:__lock)
                  00004045    00000005                       : _lock.c.obj (.cinit:__unlock)
                  0000404a    00000004     externInputInterface.obj (.cinit)
                  0000404e    00000002     --HOLE-- [fill = 0]

.pinit     0    00004000    00000000     UNINITIALIZED

ramfuncs   0    00004050    0000001f     
                  00004050    0000001f     main.obj (ramfuncs)

codestart 
*          0    0000406f    00000002     
                  0000406f    00000002     CodeStartBranch.obj (codestart)

.text      0    00005000    00000245     
                  00005000    0000010e     stateMachine.obj (.text)
                  0000510e    00000056     rts2800_fpu32.lib : boot28.asm.obj (.text)
                  00005164    00000029                       : exit.c.obj (.text)
                  0000518d    00000028     main.obj (.text)
                  000051b5    00000024     rts2800_fpu32.lib : cpy_tbl.c.obj (.text)
                  000051d9    00000020     externInputInterface.obj (.text)
                  000051f9    0000001d     rts2800_fpu32.lib : memcpy.c.obj (.text)
                  00005216    00000019                       : args_main.c.obj (.text)
                  0000522f    0000000a     main.obj (.text:retain)
                  00005239    00000009     rts2800_fpu32.lib : _lock.c.obj (.text)
                  00005242    00000002                       : pre_init.c.obj (.text)
                  00005244    00000001                       : startup.c.obj (.text)

.reset     0    003fffc0    00000002     DSECT
                  003fffc0    00000002     rts2800_fpu32.lib : boot28.asm.obj (.reset)

vectors    0    003fffc2    00000000     DSECT

.stack     1    00000000    00000300     UNINITIALIZED
                  00000000    00000300     --HOLE--

.ebss      1    00008000    00000019     UNINITIALIZED
                  00008000    0000000a     stateMachine.obj (.ebss)
                  0000800a    00000006     rts2800_fpu32.lib : exit.c.obj (.ebss)
                  00008010    00000004     main.obj (.ebss)
                  00008014    00000002     rts2800_fpu32.lib : _lock.c.obj (.ebss:__lock)
                  00008016    00000002                       : _lock.c.obj (.ebss:__unlock)
                  00008018    00000001     externInputInterface.obj (.ebss)

MODULE SUMMARY

       Module                     code   initialized data   uninitialized data
       ------                     ----   ----------------   ------------------
    .\
       stateMachine.obj           270    40                 10                
       main.obj                   81     10                 4                 
       externInputInterface.obj   32     4                  1                 
       CodeStartBranch.obj        2      0                  0                 
    +--+--------------------------+------+------------------+--------------------+
       Total:                     385    54                 15                
                                                                              
    D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\rts2800_fpu32.lib
       boot28.asm.obj             86     0                  0                 
       exit.c.obj                 41     14                 6                 
       cpy_tbl.c.obj              36     0                  0                 
       memcpy.c.obj               29     0                  0                 
       args_main.c.obj            25     0                  0                 
       _lock.c.obj                9      10                 4                 
       pre_init.c.obj             2      0                  0                 
       startup.c.obj              1      0                  0                 
    +--+--------------------------+------+------------------+--------------------+
       Total:                     229    24                 10                
                                                                              
       Stack:                     0      0                  768               
    +--+--------------------------+------+------------------+--------------------+
       Grand Total:               614    78                 793               


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000000       0 (00000000)     __stack

00008000     200 (00008000)     _TF1_2_condition
00008001     200 (00008000)     _powerOn
00008002     200 (00008000)     _standby
00008003     200 (00008000)     _TF0_1_condition
00008004     200 (00008000)     _commissioning
00008005     200 (00008000)     _current_state
00008006     200 (00008000)     _emergency
00008007     200 (00008000)     _normal
00008008     200 (00008000)     _manual
00008009     200 (00008000)     _backupTest
0000800a     200 (00008000)     ___TI_enable_exit_profile_output
0000800c     200 (00008000)     ___TI_cleanup_ptr
0000800e     200 (00008000)     ___TI_dtors_ptr
00008014     200 (00008000)     __lock
00008016     200 (00008000)     __unlock
00008018     200 (00008000)     _DI


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                            
----  -------   ----                            
0     00005000  .text                           
0     00005164  C$$EXIT                         
0     00005009  _CLR_BIT                        
1     00008018  _DI                             
0     0000522f  _DefaultIsr                     
0     0000518d  _DelayMs                        
0     000051ab  _InitPieVectTable               
0     00004050  _InitSystem                     
0     00005000  _SET_BIT                        
1     00008003  _TF0_1_condition                
1     00008000  _TF1_2_condition                
1     00000300  __STACK_END                     
abs   00000300  __STACK_SIZE                    
1     0000800c  ___TI_cleanup_ptr               
1     0000800e  ___TI_dtors_ptr                 
1     0000800a  ___TI_enable_exit_profile_output
abs   ffffffff  ___TI_pprof_out_hndl            
abs   ffffffff  ___TI_prof_data_size            
abs   ffffffff  ___TI_prof_data_start           
abs   ffffffff  ___binit__                      
abs   ffffffff  ___c_args__                     
0     00004000  ___cinit__                      
0     00005245  ___etext__                      
abs   ffffffff  ___pinit__                      
0     00005000  ___text__                       
0     00005216  __args_main                     
0     000051af  __c_int00                       
1     00008014  __lock                          
0     00005241  __nop                           
0     0000523d  __register_lock                 
0     00005239  __register_unlock               
1     00000000  __stack                         
0     00005244  __system_post_cinit             
0     00005242  __system_pre_init               
1     00008016  __unlock                        
0     00005164  _abort                          
1     00008009  _backupTest                     
0     0000510e  _c_int00                        
1     00008004  _commissioning                  
0     000051b5  _copy_in                        
1     00008005  _current_state                  
1     00008006  _emergency                      
0     00005166  _exit                           
0     00005013  _get_current_state              
0     000051d9  _input_tf0_1                    
0     000051ec  _input_tf1_2                    
0     00004065  _main                           
1     00008008  _manual                         
0     000051f9  _memcpy                         
1     00008007  _normal                         
1     00008001  _powerOn                        
1     00008002  _standby                        
0     00005096  _state_machine_process          
abs   ffffffff  binit                           
0     00004000  cinit                           
0     0000406f  code_start                      
0     00005245  etext                           
abs   ffffffff  pinit                           


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                            
----  -------   ----                            
0     00004000  ___cinit__                      
0     00004000  cinit                           
0     00004050  _InitSystem                     
0     00004065  _main                           
0     0000406f  code_start                      
0     00005000  .text                           
0     00005000  _SET_BIT                        
0     00005000  ___text__                       
0     00005009  _CLR_BIT                        
0     00005013  _get_current_state              
0     00005096  _state_machine_process          
0     0000510e  _c_int00                        
0     00005164  C$$EXIT                         
0     00005164  _abort                          
0     00005166  _exit                           
0     0000518d  _DelayMs                        
0     000051ab  _InitPieVectTable               
0     000051af  __c_int00                       
0     000051b5  _copy_in                        
0     000051d9  _input_tf0_1                    
0     000051ec  _input_tf1_2                    
0     000051f9  _memcpy                         
0     00005216  __args_main                     
0     0000522f  _DefaultIsr                     
0     00005239  __register_unlock               
0     0000523d  __register_lock                 
0     00005241  __nop                           
0     00005242  __system_pre_init               
0     00005244  __system_post_cinit             
0     00005245  ___etext__                      
0     00005245  etext                           
1     00000000  __stack                         
1     00000300  __STACK_END                     
1     00008000  _TF1_2_condition                
1     00008001  _powerOn                        
1     00008002  _standby                        
1     00008003  _TF0_1_condition                
1     00008004  _commissioning                  
1     00008005  _current_state                  
1     00008006  _emergency                      
1     00008007  _normal                         
1     00008008  _manual                         
1     00008009  _backupTest                     
1     0000800a  ___TI_enable_exit_profile_output
1     0000800c  ___TI_cleanup_ptr               
1     0000800e  ___TI_dtors_ptr                 
1     00008014  __lock                          
1     00008016  __unlock                        
1     00008018  _DI                             
abs   00000300  __STACK_SIZE                    
abs   ffffffff  ___TI_pprof_out_hndl            
abs   ffffffff  ___TI_prof_data_size            
abs   ffffffff  ___TI_prof_data_start           
abs   ffffffff  ___binit__                      
abs   ffffffff  ___c_args__                     
abs   ffffffff  ___pinit__                      
abs   ffffffff  binit                           
abs   ffffffff  pinit                           

[58 symbols]
